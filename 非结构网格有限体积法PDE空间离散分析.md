# 非结构网格有限体积法PDE空间离散过程分析

## 1. 概述

基于代码分析，该项目实现了一个完整的非结构网格有限体积法求解器，用于求解不可压缩Navier-Stokes方程。整个空间离散过程包括网格预处理、控制体积构建、梯度重构、通量计算和边界条件处理等关键步骤。

## 2. 网格数据结构与预处理

### 2.1 基本网格元素
```python
# 核心网格数据结构 (parse_to_h5.py)
mesh_pos = dataset["node|pos"]           # 顶点坐标 [N_nodes, 2]
face_node = dataset["face|face_node"]    # 面-顶点连接关系 [2, N_faces]
cells_node = dataset["cells_node"]       # 单元-顶点连接关系
cells_face = dataset["cells_face"]       # 单元-面连接关系
cells_index = dataset["cells_index"]     # 单元索引
```

### 2.2 控制体积构建
控制体积的构建是有限体积法的核心，代码中通过以下步骤实现：

#### 2.2.1 单元质心计算
```python
# 计算单元质心坐标
centroid = scatter(
    src=mesh_pos[cells_node],
    index=cells_index,
    dim=0,
    reduce="mean",
)
```

#### 2.2.2 面中心计算
```python
# 计算面中心坐标
face_center_pos = (mesh_pos[face_node[0]] + mesh_pos[face_node[1]]) / 2.0
```

#### 2.2.3 顶点逆时针排序
为确保数值稳定性，代码实现了顶点的逆时针排序：
```python
def sort_vertices_ccw(mesh_pos, face_center, cells_node, cells_face, cells_index, centroid):
    # 使用角度排序确保逆时针方向
    relative_coords_vertices = vertices_coords - cell_centroids.unsqueeze(1)
    angles_vertices = torch.atan2(relative_coords_vertices[:, :, 1], relative_coords_vertices[:, :, 0])
    sorted_indices_vertices = torch.argsort(angles_vertices, dim=1)
```

## 3. 几何量计算

### 3.1 面积计算
代码使用两种方法计算单元面积：

#### 3.1.1 面积分方法
```python
# 使用面积分计算单元面积
surface_vector = cells_face_unv * cells_face_area
full_synataic_function = 0.5 * face_center_pos[cells_face.view(-1)]
cells_area = scatter(
    src=(full_synataic_function * surface_vector).sum(dim=1, keepdim=True),
    index=cells_index,
    reduce="sum",
    dim=0,
)
```

#### 3.1.2 鞋带公式验证
```python
def polygon_area(vertices):
    """使用shoelace formula计算多边形面积"""
    x = vertices[:, 0]
    y = vertices[:, 1]
    return 0.5 * np.abs(np.dot(x, np.roll(y, 1)) - np.dot(y, np.roll(x, 1)))
```

### 3.2 法向量计算
```python
# 计算单位法向量
pos_diff = mesh_pos[senders_node] - mesh_pos[recivers_node]
unv = torch.cat((-pos_diff[:, 1:2], pos_diff[:, 0:1]), dim=1)
unv = unv / (torch.norm(unv, dim=1, keepdim=True))

# 确保法向量指向正确方向
face_to_centroid = face_center_pos[cells_face.view(-1)] - centroid[cells_index.view(-1)]
cells_face_unv = unv[cells_face.view(-1)]
unv_dir_mask = (torch.sum(face_to_centroid * cells_face_unv, dim=1, keepdim=True) > 0.0)
cells_face_unv = torch.where(unv_dir_mask, cells_face_unv, (-1.0) * cells_face_unv)
```

## 4. 梯度重构

### 4.1 加权最小二乘法(WLSQ)
代码实现了高阶精度的梯度重构方法：

```python
def weighted_lstsq(phi_node, edge_index, mesh_pos, order, precompute_Moments):
    """
    基于节点的加权最小二乘梯度重构
    支持1阶到4阶精度
    """
    # 构建法向量矩阵A和右端项B
    A_normalized, B_normalized = construct_WLSQ_matrices(...)
    
    # 求解线性系统
    nabla_phi_node_lst = torch.linalg.solve(A_normalized, B_normalized).transpose(1, 2)
    
    return nabla_phi_node_lst  # [N, C, grad_dim]
```

### 4.2 模板构建
```python
def construct_stencil(mesh, k_hop=2, BC_interal_neighbors=4, order=None):
    """构建梯度重构所需的邻居模板"""
    # 构建k跳邻居关系
    neighbor_cell_x = build_k_hop_edge_index(cpd_neighbor_cell, k_hop)
    
    # 为边界单元添加额外邻居
    extra_boundary_edge = knn_graph(cpd_centroid[BC_face_mask], k=BC_interal_neighbors)
```

## 5. 通量计算

### 5.1 守恒形式
代码实现了守恒形式的有限体积离散：

```python
def conserved_form(self, uvp_new, grad_phi, graph_cell, ...):
    """守恒形式的有限体积方程"""
    
    # 对流通量
    convection_flux = uu_flux[cells_face] * convection_coefficent[cells_index]
    
    # 粘性通量  
    vis_flux = grad_uv_face_hat[cells_face] * diffusion_coefficent[cells_index]
    
    # 压力项
    P_flux = torch.diag_embed(p_face_new[cells_face]) * grad_p_coefficent[cells_index]
    
    # 总通量
    J_flux = torch.matmul(
        convection_flux + P_flux - vis_flux, 
        cells_face_surface_vec.unsqueeze(-1)
    ).squeeze()
    
    # 通量积分
    total_RHS = scatter_add(J_flux, cells_index, dim=0) - source_term
```

### 5.2 非守恒形式
```python
def non_conserved_form(self, uvp_new, grad_phi, ...):
    """非守恒形式的有限体积方程"""
    
    # 基于梯度的对流项
    convection_cell = torch.matmul(
        grad_uv_cell_hat[mask_interior_cell], 
        uv_cell_hat[mask_interior_cell].unsqueeze(2)
    ).squeeze() * cells_area
    
    # 基于散度的扩散项
    viscosity_force_cells_face = torch.matmul(
        grad_uv_face_hat[cells_face, 0:2],
        cells_face_surface_vec.unsqueeze(2),
    ).squeeze()
```

## 6. 插值方法

### 6.1 高阶插值
```python
def interpolating_phic_to_faces(self, phi_cell, grad_phi_cell, neighbor_cell, ...):
    """单元中心到面中心的高阶插值"""
    # 基于距离加权的线性插值
    # 使用梯度信息进行修正
    return interpolated_values
```

### 6.2 径向基函数插值
```python
def rbf_interpolate(self, phi_values, source_pos, target_pos, ...):
    """通用RBF插值函数"""
    # 计算RBF核函数
    kernel_values = torch.sqrt(distances_squared + shape_param_sq)
    # 插值计算
    result = torch.sum(kernel_target * coeffs, dim=1)
```

## 7. 边界条件处理

### 7.1 边界面识别
```python
# 根据节点类型识别边界面
face_type = torch.full((face_node.shape[1],), NodeType.NORMAL).long()
left_node_type, right_node_type = node_type[face_node[0]], node_type[face_node[1]]

# 入口边界
mask_inflow_face = ((left_node_type==NodeType.INFLOW) & (right_node_type==NodeType.INFLOW))
face_type[mask_inflow_face] = NodeType.INFLOW

# 壁面边界
mask_wall_face = ((left_node_type==NodeType.WALL_BOUNDARY) & (right_node_type==NodeType.WALL_BOUNDARY))
face_type[mask_wall_face] = NodeType.WALL_BOUNDARY
```

### 7.2 边界条件实施
```python
def velocity_profile(inlet_node_pos, mean_u, aoa, inlet_type):
    """设置入口边界条件"""
    if inlet_type == "uniform":
        inlet_field[:, 0] = torch.full_like(inlet_field[:, 0], float(mean_u))
    elif inlet_type == "parabolic":
        # 抛物线速度分布
        inlet_field[:, 0] = 6 * mean_u * y_positions * ((max_y - y_positions) / max_y**2)
    elif inlet_type == "Taylor_Green":
        # Taylor-Green涡流
        inlet_field[:, 0] = mean_u * torch.sin(2*π*x) * torch.cos(2*π*y)
        inlet_field[:, 1] = -mean_u * torch.cos(2*π*x) * torch.sin(2*π*y)
```

## 8. 数值特点

### 8.1 优势
1. **高阶精度**: 支持1-4阶WLSQ梯度重构
2. **几何灵活性**: 支持任意多边形单元
3. **守恒性**: 严格满足守恒定律
4. **稳定性**: 通过逆时针排序和法向量校正确保数值稳定

### 8.2 关键技术
1. **模板自适应**: 根据边界条件动态调整重构模板
2. **多种插值**: RBF、线性插值等多种方法
3. **边界处理**: 虚拟单元法处理复杂边界
4. **验证机制**: 多种几何量计算方法相互验证

这个实现展现了现代CFD求解器的典型特征，结合了传统有限体积法的守恒性和现代数值方法的高精度特性。

## 9. 关键算法实现细节

### 9.1 邻居单元关系构建
```python
# 计算邻居单元关系 (parse_to_h5.py:391-409)
senders_cell = scatter(
    src=cells_index[:,None],
    index=cells_face,
    dim=0,
    reduce="max"
).squeeze(1)

recivers_cell = scatter(
    src=cells_index[:,None],
    index=cells_face,
    dim=0,
    reduce="min"
).squeeze(1)

neighbor_cell = torch.stack((recivers_cell, senders_cell), dim=0)
```

### 9.2 复合邻居单元处理
为处理边界条件，代码创建了虚拟边界单元：
```python
# 边界面处理 (parse_to_h5.py:417-435)
BC_face_mask = ~(face_type==NodeType.NORMAL).squeeze()
BC_face_center_pos = face_center_pos[BC_face_mask]
BC_face_center_pos_ptr = torch.arange(BC_face_center_pos.shape[0]) + centroid.shape[0]

# 创建复合质心坐标
cpd_centroid = torch.cat((centroid, BC_face_center_pos), dim=0)

# 更新邻居关系
cpd_neighbor_cell = neighbor_cell.clone()
cpd_neighbor_cell[1, BC_face_mask] = BC_face_center_pos_ptr
```

### 9.3 面积分验证机制
```python
# 面积分计算 (parse_to_h5.py:594-601)
surface_vector = cells_face_unv * cells_face_area
full_synataic_function = 0.5 * face_center_pos[cells_face.view(-1)]
cells_area = scatter(
    src=(full_synataic_function * surface_vector).sum(dim=1, keepdim=True),
    index=cells_index,
    reduce="sum",
    dim=0,
)

# 鞋带公式验证 (parse_to_h5.py:604-619)
test_cells_area = []
for i in range(cells_index.max().numpy() + 1):
    test_cells_area.append(
        polygon_area(mesh_pos[cells_node[(cells_index == i).view(-1)].view(-1)])
    )
test_cells_area = torch.from_numpy(np.asarray(test_cells_area))

# 验证一致性
if not torch.allclose(cells_area, test_cells_area, rtol=1e-05, atol=1e-08):
    dataset["cell|cells_area"] = test_cells_area.unsqueeze(1)
    print(f"warning: substituted cells area with shoelace formula")
```

## 10. 数值方法特点分析

### 10.1 WLSQ梯度重构的优势
1. **高阶精度**: 支持1-4阶Taylor展开
2. **几何适应性**: 适用于任意多边形网格
3. **边界处理**: 通过扩展模板处理边界单元
4. **数值稳定性**: 通过预条件和归一化提高稳定性

### 10.2 通量计算的双重形式
代码同时实现了守恒形式和非守恒形式：

**守恒形式优势**:
- 严格满足守恒定律
- 适用于激波等不连续问题
- 数值通量具有物理意义

**非守恒形式优势**:
- 计算效率更高
- 梯度信息直接可用
- 适用于光滑解问题

### 10.3 边界条件的物理实现
```python
# Taylor-Green涡流初始化 (Set_BC.py:54-60)
elif "Taylor_Green" == inlet_type:
    x = inlet_node_pos[:, 0]
    y = inlet_node_pos[:, 1]
    inlet_field[:, 0] = mean_u*torch.sin(2*π*x)*torch.cos(2*π*y)
    inlet_field[:, 1] = -mean_u*torch.cos(2*π*x)*torch.sin(2*π*y)
    pressure_field = (-(1/4)*mean_u*(torch.cos(4*π*x) + torch.cos(4*π*y)))[:,None]
```

## 11. 计算复杂度分析

### 11.1 空间复杂度
- 网格存储: O(N_cells + N_faces + N_nodes)
- 梯度重构矩阵: O(N_cells × k²) (k为邻居数)
- 通量计算: O(N_faces)

### 11.2 时间复杂度
- 梯度重构: O(N_cells × k³) (矩阵求解)
- 通量计算: O(N_faces)
- 边界条件: O(N_boundary_faces)

## 12. 数值验证策略

### 12.1 几何守恒验证
```python
# 验证面积分守恒 (parse_to_h5.py:577-585)
valid = scatter(
    src=surface_vector,
    index=cells_index,
    reduce="sum",
    dim=0,
)
if not torch.allclose(valid, torch.zeros_like(valid), rtol=1e-05, atol=1e-08):
    raise ValueError(f"wrong unv calculation")
```

### 12.2 法向量方向验证
```python
# 确保法向量指向外法向 (parse_to_h5.py:563-572)
face_to_centroid = face_center_pos[cells_face.view(-1)] - centroid[cells_index.view(-1)]
cells_face_unv = unv[cells_face.view(-1)]
unv_dir_mask = (torch.sum(face_to_centroid * cells_face_unv, dim=1, keepdim=True) > 0.0)
cells_face_unv = torch.where(unv_dir_mask, cells_face_unv, (-1.0) * cells_face_unv)
```

## 13. 工程实现亮点

### 13.1 模块化设计
- 网格处理模块 (`Extract_mesh/`)
- 有限体积离散模块 (`FVMmodel/FVdiscretization/`)
- 边界条件模块 (`Load_mesh/Set_BC.py`)
- 后处理模块 (`Post_process/`)

### 13.2 GPU加速优化
- 使用PyTorch张量操作
- 向量化计算避免循环
- 内存访问优化

### 13.3 可视化支持
```python
# 网格可视化 (parse_to_h5.py:441-549)
def visualize_graph_structure(mesh_pos, face_node, centroid, cpd_centroid_pos, neighbor_cell):
    # 创建NetworkX图进行可视化
    # 区分原始单元和边界虚拟单元
    # 保存高质量图像
```

这个实现代表了现代CFD求解器的先进水平，融合了数值分析、计算几何和高性能计算的最新成果。

## 14. U(L)和U(R)构造方法详细分析

### 14.1 概述
项目中并未采用传统的MUSCL格式，而是使用了基于WLSQ梯度重构的高阶插值方法来构造面值。这种方法在非结构网格上具有更好的几何适应性和数值精度。

### 14.2 主要插值方法

#### 14.2.1 高阶距离加权插值 (`interpolating_phic_to_faces`)
这是项目中构造面值的核心方法，基于Moukalled等人的理论：

```python
def interpolating_phic_to_faces(self, phi_cell, grad_phi_cell, neighbor_cell, centroid, face_center_pos):
    """
    单元中心到面中心的高阶插值
    实现Moukalled书中第9.2节的插值格式
    """
    # 获取面两侧的单元索引
    C_senders, F_recivers = neighbor_cell[0], neighbor_cell[1]  # 对应书中的C和F

    # 计算几何权重因子
    dCF = torch.norm(centroid[F_recivers] - centroid[C_senders], dim=1, keepdim=True)
    gC = (torch.norm(centroid[F_recivers] - face_center_pos, dim=1, keepdim=True) / dCF)[:,:,None]
    gF = 1. - gC

    # 计算位置向量
    eCf = (face_center_pos - centroid[C_senders])[:,:,None]
    eFf = (face_center_pos - centroid[F_recivers])[:,:,None]

    # 线性插值
    phi_f_hat = phi_cell[C_senders] * gC + phi_cell[F_recivers] * gF

    # 梯度修正项
    correction = gC * grad_phi_cell[C_senders] @ eCf + gF * grad_phi_cell[F_recivers] @ eFf

    return (phi_f_hat + correction).squeeze()
```

**数学原理**：
- 基本插值：φ_f = g_C φ_C + g_F φ_F
- 梯度修正：φ_f = φ_f_hat + g_C (∇φ_C · e_Cf) + g_F (∇φ_F · e_Ff)
- 权重因子：g_C = |r_F - r_f| / |r_F - r_C|, g_F = 1 - g_C

#### 14.2.2 二阶精度单元到节点插值 (`cell_to_node_2nd_order`)
用于从单元中心插值到节点：

```python
def cell_to_node_2nd_order(self, cell_phi, cell_grad, cells_node, cells_index, centroid, mesh_pos):
    """
    二阶精度的单元到节点插值
    """
    # 计算位置向量
    mesh_pos_to_centroid = mesh_pos[cells_node] - centroid[cells_index]

    # 距离权重
    weight = 1.0 / torch.norm(mesh_pos_to_centroid, dim=-1, keepdim=True)

    if cell_grad is not None:
        # 一阶修正项
        first_order_correction = torch.matmul(
            mesh_pos_to_centroid.unsqueeze(1).unsqueeze(1),
            cell_grad[cells_node].unsqueeze(-1)
        ).squeeze()

        aggrate_cell_attr = (cell_phi[cells_index] + first_order_correction) * weight
    else:
        aggrate_cell_attr = cell_phi[cells_index] * weight

    # 加权平均
    cell_to_node = scatter_add(aggrate_cell_attr, cells_node, dim=0) / \
                   scatter_add(weight, cells_node, dim=0)

    return cell_to_node
```

### 14.3 梯度重构方法

#### 14.3.1 WLSQ梯度重构
项目使用加权最小二乘法重构梯度，支持1-4阶精度：

```python
def weighted_lstsq(phi_node, edge_index, mesh_pos, order, precompute_Moments):
    """
    加权最小二乘梯度重构
    """
    # 构建系数矩阵A和右端项B
    A_cell_to_cell, single_B_cell_to_cell = precompute_Moments[0], precompute_Moments[1]

    # 计算右端项
    outdegree_node_index, indegree_node_index = edge_index[0], edge_index[1]
    phi_diff_on_edge = (phi_node[indegree_node_index] - phi_node[outdegree_node_index])

    B_phi_cell_to_cell = scatter_add(
        phi_diff_on_edge, outdegree_node_index, dim=0, dim_size=mesh_pos.shape[0]
    )

    # 归一化处理
    row_norms = torch.norm(A_cell_to_cell, p=2, dim=2, keepdim=True)
    A_normalized = A_cell_to_cell / (row_norms + 1e-8)
    B_normalized = B_phi_cell_to_cell / (row_norms + 1e-8)

    # 求解线性系统
    nabla_phi_node_lst = torch.linalg.solve(A_normalized, B_normalized).transpose(1, 2)

    return nabla_phi_node_lst
```

#### 14.3.2 模板构建
为确保梯度重构的稳定性，项目构建了扩展的邻居模板：

```python
def construct_stencil(mesh, k_hop=2, BC_interal_neighbors=4, order=None):
    """
    构建梯度重构模板
    """
    # 基本邻居关系
    cpd_neighbor_cell = mesh["cpd|neighbor_cell"].long()

    # k跳邻居扩展
    extra_edge_index = build_k_hop_edge_index(cpd_neighbor_cell, k_hop)

    # 边界单元特殊处理
    BC_face_mask = ~(cell_type == NodeType.NORMAL)
    if BC_face_mask.any():
        extra_boundary_edge = knn_graph(
            cpd_centroid[BC_face_mask],
            k=BC_interal_neighbors
        )

    # 合并所有邻居关系
    mesh["neighbor_cell_x"] = torch.cat((
        cpd_neighbor_cell,
        extra_edge_index_unique,
        extra_boundary_edge_unique
    ), dim=1)
```

### 14.4 与传统MUSCL方法的对比

#### 14.4.1 传统MUSCL方法
```
U_L = U_i + 0.5 * φ(r_i) * ∇U_i · Δx_i
U_R = U_j - 0.5 * φ(r_j) * ∇U_j · Δx_j
```
其中φ(r)是限制器函数。

#### 14.4.2 项目采用的方法
```
φ_f = g_C φ_C + g_F φ_F + g_C (∇φ_C · e_Cf) + g_F (∇φ_F · e_Ff)
```

**优势对比**：
1. **几何适应性**：项目方法自然适应非结构网格
2. **精度**：通过WLSQ可达到高阶精度
3. **稳定性**：距离加权避免了传统限制器的复杂性
4. **一致性**：在任意网格上保持数值一致性

### 14.5 RBF插值作为补充方法

项目还实现了径向基函数插值作为备选：

```python
def rbf_interpolate(self, phi_values, source_pos, target_pos, source_indices, target_indices, k=4):
    """
    RBF插值方法
    """
    # 计算距离
    distances_squared = torch.sum((source_pos_neighbors - target_pos_expanded)**2, dim=2)

    # RBF核函数
    kernel_values = torch.sqrt(distances_squared + shape_param_sq)

    # 插值计算
    result = torch.sum(kernel_target * coeffs, dim=1)

    return result
```

### 14.6 实际应用流程

在求解器中，面值构造的完整流程为：

1. **梯度重构**：使用WLSQ方法计算单元梯度
2. **面值插值**：使用`interpolating_phic_to_faces`计算面中心值
3. **通量计算**：基于面值计算数值通量
4. **边界处理**：对边界面应用相应边界条件

这种方法避免了传统MUSCL格式中复杂的限制器设计，在保证高精度的同时具有更好的数值稳定性。
