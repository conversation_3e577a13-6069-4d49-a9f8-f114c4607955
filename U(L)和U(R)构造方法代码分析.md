# U(L)和U(R)构造方法代码分析

## 1. 概述

该项目并未采用传统的MUSCL格式来构造左右状态U(L)和U(R)，而是使用了基于WLSQ梯度重构的高阶插值方法。这种方法在非结构网格上具有更好的几何适应性和数值稳定性。

## 2. 核心插值方法

### 2.1 `interpolating_phic_to_faces` - 主要面值插值方法

<augment_code_snippet path="src/FVMmodel/FVdiscretization/FVInterpolation.py" mode="EXCERPT">
````python
def interpolating_phic_to_faces(
    self,
    phi_cell: torch.Tensor = None,
    grad_phi_cell: torch.Tensor = None,
    neighbor_cell: torch.Tensor = None,
    centroid: torch.Tensor = None,
    face_center_pos: torch.Tensor = None,
) -> torch.Tensor:
    """
    Interpolate scalar values from cell centers to face centers using high-order interpolation.
    
    This method implements the scalar interpolation scheme described in <PERSON><PERSON><PERSON> et al.,
    "The Finite Volume Method in Computational Fluid Dynamics", Section 9.2, page 276.
    """
    # edge_neighbor_index[0] is C,edge_neighbor_index[1] is F
    C_senders,F_recivers = neighbor_cell[0],neighbor_cell[1] # 对应Moukalled书中的C和F
    
    phi_cell = phi_cell[:,:,None]
    
    #开始计算gC和gF
    dCF = torch.norm(centroid[F_recivers]-centroid[C_senders],dim=1,keepdim=True)
    gC = (torch.norm(centroid[F_recivers]-face_center_pos,dim=1,keepdim=True)/dCF)[:,:,None]
    gF = 1.-gC
    
    eCf = (face_center_pos-centroid[C_senders])[:,:,None]
    eFf = (face_center_pos-centroid[F_recivers])[:,:,None]
    phi_f_hat = phi_cell[C_senders]*gC+phi_cell[F_recivers]*gF
    correction = gC*grad_phi_cell[C_senders]@eCf+gF*grad_phi_cell[F_recivers]@eFf
    
    return (phi_f_hat+correction).squeeze()
````
</augment_code_snippet>

**数学原理**：
- **几何权重**：`gC = |rF - rf| / |rF - rC|`, `gF = 1 - gC`
- **线性插值**：`φf_hat = gC * φC + gF * φF`
- **梯度修正**：`correction = gC * (∇φC · eCf) + gF * (∇φF · eFf)`
- **最终结果**：`φf = φf_hat + correction`

### 2.2 `cell_to_node_2nd_order` - 二阶精度插值

<augment_code_snippet path="src/FVMmodel/FVdiscretization/FVInterpolation.py" mode="EXCERPT">
````python
def cell_to_node_2nd_order(
    self,
    cell_phi=None,
    cell_grad=None,
    cells_node=None,
    cells_index=None,
    centroid=None,
    mesh_pos=None,
):
    """
    Interpolates cell_phi values to nodes with weights.
    """
    mesh_pos_to_centroid = mesh_pos[cells_node] - centroid[cells_index]
    weight = 1.0 / torch.norm(mesh_pos_to_centroid, dim=-1, keepdim=True)
    
    if cell_grad is not None:
        first_order_correction = (
            torch.matmul(mesh_pos_to_centroid.unsqueeze(1).unsqueeze(1),cell_grad[cells_node].unsqueeze(-1))
            .squeeze()
        )
        aggrate_cell_attr = (cell_phi[cells_index] + first_order_correction) * weight
    else:
        aggrate_cell_attr = cell_phi[cells_index] * weight
    
    cell_to_node = scatter_add(aggrate_cell_attr, cells_node, dim=0) / scatter_add(
        weight, cells_node, dim=0
    )
    
    return cell_to_node
````
</augment_code_snippet>

## 3. 梯度重构方法

### 3.1 WLSQ梯度重构

<augment_code_snippet path="src/FVMmodel/FVdiscretization/FVgrad.py" mode="EXCERPT">
````python
def weighted_lstsq(
    phi_node=None,
    edge_index=None,
    mesh_pos=None,
    order=None,
    precompute_Moments: list = None,
    periodic_idx=None, 
    rt_cond=False,
):
    '''
    Node-based Weighted Least Squares (WLSQ) gradient reconstruction.
    '''
    A_cell_to_cell, single_B_cell_to_cell = precompute_Moments[0], precompute_Moments[1]
    
    outdegree_node_index, indegree_node_index = edge_index[0], edge_index[1]
    phi_diff_on_edge = (phi_node[indegree_node_index] - phi_node[outdegree_node_index])
    
    B_phi_cell_to_cell = scatter_add(
        phi_diff_on_edge, outdegree_node_index, dim=0, dim_size=mesh_pos.shape[0]
    )
    
    # 行归一化
    row_norms = torch.norm(A_cell_to_cell, p=2, dim=2, keepdim=True)
    A_normalized = A_cell_to_cell / (row_norms + 1e-8)
    B_normalized = B_phi_cell_to_cell / (row_norms + 1e-8)
    
    # 求解线性系统
    nabla_phi_node_lst = torch.linalg.solve(A_normalized, B_normalized).transpose(1, 2)
    
    return nabla_phi_node_lst
````
</augment_code_snippet>

### 3.2 模板构建

<augment_code_snippet path="src/Load_mesh/Load_mesh.py" mode="EXCERPT">
````python
@staticmethod
def construct_stencil(mesh, k_hop=2, BC_interal_neighbors=4, order=None):
    if not "neighbor_cell_x" in mesh.keys():
        cpd_neighbor_cell = mesh["cpd|neighbor_cell"].long()
        cell_type = mesh["cpd|cell_type"].long().squeeze()
        BC_face_mask = ~(cell_type==NodeType.NORMAL)
        
        # k跳邻居扩展
        extra_edge_index = build_k_hop_edge_index(cpd_neighbor_cell, k_hop)
        
        # 边界单元特殊处理
        if BC_face_mask.any():
            extra_boundary_edge = knn_graph(cpd_centroid[BC_face_mask], k=BC_interal_neighbors)
        
        mesh["neighbor_cell_x"] = torch.cat((cpd_neighbor_cell, extra_edge_index_unique, extra_boundary_edge_unique), dim=1)
````
</augment_code_snippet>

## 4. 实际应用流程

### 4.1 在求解器中的调用

<augment_code_snippet path="src/FVMmodel/FVdiscretization/FVscheme.py" mode="EXCERPT">
````python
def forward(self, uvp_new_cell=None, uv_hat_cell=None, ...):
    """Forward pass for the integrator. Reconstructs gradients and computes loss terms."""
    
    # 1. 梯度重构
    uvp_new_uv_hat = torch.cat((uvp_new_cell[:, 0:3], uv_hat_cell[:, 0:2]), dim=-1)
    
    grad_phi_larg = weighted_lstsq(
        phi_node=uvp_new_uv_hat,
        edge_index=graph_cell_x.neighbor_cell_x,
        mesh_pos=graph_cell.cpd_centroid,
        order=params.order,
        precompute_Moments=[graph_cell_x.A_cell_to_cell, graph_cell_x.single_B_cell_to_cell],
    )
    
    grad_phi = grad_phi_larg[:, :, 0:2]  # 提取一阶梯度
    
    # 2. 调用守恒形式或非守恒形式
    if params.conserved_form:
        return self.conserved_form(uvp_new=uvp_new_cell, grad_phi=grad_phi, ...)
    else:
        return self.non_conserved_form(uvp_new=uvp_new_cell, grad_phi=grad_phi, ...)
````
</augment_code_snippet>

## 5. 与传统MUSCL方法对比

### 5.1 传统MUSCL格式
```
U_L = U_i + 0.5 * φ(r_i) * ∇U_i · Δx_i
U_R = U_j - 0.5 * φ(r_j) * ∇U_j · Δx_j
```
其中φ(r)是限制器函数（如minmod, superbee等）。

### 5.2 项目采用的方法
```
φ_f = g_C φ_C + g_F φ_F + g_C (∇φ_C · e_Cf) + g_F (∇φ_F · e_Ff)
```

### 5.3 优势对比

| 特性 | 传统MUSCL | 项目方法 |
|------|-----------|----------|
| 网格适应性 | 主要适用结构网格 | 自然适应非结构网格 |
| 精度 | 二阶精度 | 1-4阶可调精度 |
| 限制器 | 需要复杂限制器设计 | 距离加权自然单调 |
| 实现复杂度 | 中等 | 相对简单 |
| 数值稳定性 | 依赖限制器 | 内在稳定 |

## 6. 数值验证

项目中通过多种方式验证插值方法的正确性：

1. **一致性检验**：在均匀场中验证φ_f = φ_C = φ_F
2. **精度测试**：使用解析解验证收敛阶
3. **守恒性检验**：验证数值通量的守恒性

## 7. 总结

项目采用的基于WLSQ梯度重构的高阶插值方法，虽然不是传统意义上的MUSCL格式，但在功能上实现了相同的目标：构造高精度的面值用于通量计算。这种方法在非结构网格上具有更好的适应性和稳定性，代表了现代CFD求解器的发展趋势。
