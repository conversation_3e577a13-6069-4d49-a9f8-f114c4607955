# 非结构网格有限体积法数学公式推导

## 1. 基本控制方程

### 1.1 不可压缩Navier-Stokes方程
连续性方程：
```
∇ · u = 0
```

动量方程：
```
∂u/∂t + u · ∇u = -∇p/ρ + ν∇²u + f
```

其中：
- u: 速度矢量 [u, v]
- p: 压力
- ρ: 密度
- ν: 运动粘度
- f: 体积力

## 2. 有限体积离散

### 2.1 积分形式
对控制体积Ω积分，应用Gauss定理：

连续性方程：
```
∫∫_Ω ∇ · u dΩ = ∮_∂Ω u · n dS = 0
```

动量方程：
```
∫∫_Ω ∂u/∂t dΩ + ∮_∂Ω (u ⊗ u) · n dS = -∮_∂Ω (p/ρ)n dS + ∮_∂Ω ν(∇u) · n dS + ∫∫_Ω f dΩ
```

### 2.2 离散化
对于单元i，面j：

连续性方程离散：
```
Σ_j (u_j · n_j) A_j = 0
```

动量方程离散：
```
V_i ∂u_i/∂t + Σ_j F_j^conv = -Σ_j F_j^press + Σ_j F_j^visc + V_i f_i
```

其中：
- V_i: 单元i的体积
- A_j: 面j的面积
- n_j: 面j的外法向量
- F_j^conv: 对流通量
- F_j^press: 压力通量
- F_j^visc: 粘性通量

## 3. 通量计算

### 3.1 对流通量
```
F_j^conv = (u_j ⊗ u_j) · n_j A_j
```

面上速度u_j通过插值获得：
```
u_j = w_L u_L + w_R u_R
```

其中w_L, w_R为距离权重。

### 3.2 压力通量
```
F_j^press = (p_j/ρ) n_j A_j
```

### 3.3 粘性通量
```
F_j^visc = ν (∇u)_j · n_j A_j
```

面上梯度通过WLSQ重构获得。

## 4. 梯度重构

### 4.1 WLSQ方法
对于单元i，其邻居单元集合为N(i)，重构梯度：

```
φ(x) ≈ φ_i + ∇φ_i · (x - x_i) + O(h²)
```

最小化加权残差：
```
min Σ_{j∈N(i)} w_ij [φ_j - φ_i - ∇φ_i · (x_j - x_i)]²
```

### 4.2 权重函数
距离权重：
```
w_ij = 1/|x_j - x_i|^p
```

通常取p=2。

### 4.3 矩阵形式
```
A ∇φ_i = B
```

其中：
```
A = Σ_{j∈N(i)} w_ij (x_j - x_i) ⊗ (x_j - x_i)
B = Σ_{j∈N(i)} w_ij (φ_j - φ_i)(x_j - x_i)
```

## 5. 高阶精度

### 5.1 二阶精度
Taylor展开到二阶：
```
φ(x) ≈ φ_i + ∇φ_i · (x - x_i) + 1/2 (x - x_i)^T H_i (x - x_i)
```

其中H_i为Hessian矩阵。

### 5.2 矩阵扩展
对于二阶精度，A矩阵扩展为5×5（2D情况）：
```
A = [∂x  ∂y  ∂x²  ∂y²  ∂x∂y]^T [∂x  ∂y  ∂x²  ∂y²  ∂x∂y]
```

## 6. 边界条件

### 6.1 Dirichlet边界条件
直接指定边界值：
```
u_boundary = u_prescribed
```

### 6.2 Neumann边界条件
指定法向梯度：
```
∂u/∂n|_boundary = g_prescribed
```

### 6.3 壁面边界条件
无滑移条件：
```
u_wall = 0
```

## 7. 时间离散

### 7.1 显式Euler
```
u^{n+1} = u^n + Δt R(u^n)
```

### 7.2 隐式Euler
```
u^{n+1} = u^n + Δt R(u^{n+1})
```

### 7.3 Crank-Nicolson
```
u^{n+1} = u^n + Δt/2 [R(u^n) + R(u^{n+1})]
```

## 8. 数值稳定性

### 8.1 CFL条件
对流稳定性：
```
CFL = |u| Δt/Δx ≤ 1
```

### 8.2 扩散稳定性
```
D = ν Δt/Δx² ≤ 1/2
```

## 9. 守恒性验证

### 9.1 几何守恒
对于任意单元：
```
Σ_j n_j A_j = 0
```

### 9.2 质量守恒
全域质量守恒：
```
d/dt ∫∫_Ω ρ dΩ + ∮_∂Ω ρu · n dS = 0
```

## 10. 误差分析

### 10.1 截断误差
WLSQ方法的截断误差：
```
E_truncation = O(h^{p+1})
```

其中p为重构阶数。

### 10.2 插值误差
面值插值误差：
```
E_interpolation = O(h²)
```

### 10.3 总体精度
有限体积法总体精度：
```
E_total = O(h²)
```

## 11. 实现要点

### 11.1 数值通量
确保数值通量的一致性和守恒性。

### 11.2 梯度限制
在激波附近使用梯度限制器防止振荡。

### 11.3 压力-速度耦合
使用SIMPLE、PISO等算法处理压力-速度耦合。

这些数学公式构成了代码实现的理论基础，确保了数值方法的正确性和精度。
